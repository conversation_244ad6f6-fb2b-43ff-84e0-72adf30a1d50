<script setup>
// import type { RouteLocationMatched } from "vue-router"
import { useRouteListener } from "@/hooks/useRouteListener"
import { compile } from "path-to-regexp"

const route = useRoute()

const router = useRouter()

const { listenerRouteChange } = useRouteListener()

/** 定义响应式数据 breadcrumbs，用于存储面包屑导航信息 */
const breadcrumbs = ref([])

/** 获取面包屑导航信息 */
function getBreadcrumb() {
  breadcrumbs.value = route.matched.filter(item => item.meta?.title && item.meta?.breadcrumb !== false)
}

/** 编译路由路径 */
function pathCompile(path) {
  const toPath = compile(path)
  return toPath(route.params)
}

/** 处理面包屑导航点击事件 */
function handleLink(item) {
  const { redirect, path } = item
  if (redirect) return router.push(redirect)
  router.push(pathCompile(path))
}

const handleParentLink = (item) => {
  const { meta } = item
  if (!meta?.parentRoute) return
  router.push(pathCompile(meta.parentRoute))
}

const handleAncestorInnerParentLink = (item) => {
  const { meta } = item
  if (!meta?.parentRoute) return
  const queryParams = getQueryParams(router.options.history.state.back)
  router.push({
    path: pathCompile(meta.parentRoute),
    query: queryParams
  })
}

const handleAncestorLink = (item) => {
  const { meta } = item
  if (!meta?.ancestorRoute) return
  router.push(pathCompile(meta.ancestorRoute))
}

const getUrlTitle = (url) => {
  const titleMatch = url.match(/title=([^&]+)/)
  return titleMatch ? decodeURIComponent(titleMatch[1]) : ""
}

const getQueryParams = (url) => {
  // 获取 ? 后面的字符串
  const queryString = url.split("?")[1]
  if (!queryString) return {}

  // 将查询字符串转换为对象
  const params = {}
  queryString.split("&").forEach((param) => {
    const [key, value] = param.split("=")
    params[key] = decodeURIComponent(value)
  })
  return params
}

// 监听路由变化，更新面包屑导航信息
listenerRouteChange((route) => {
  if (route.path.startsWith("/redirect/")) return
  getBreadcrumb()
}, true)
</script>

<template>
  <div class="flex-start-center pl20">
    <!-- <img class="mr8" src="@/assets/layouts/nav-bar-icon.png" width="13" height="16" alt="" /> -->
    <el-breadcrumb>
      <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
        <span v-if="item.redirect === 'noRedirect' || index === breadcrumbs.length - 1">
          <span v-if="item.meta.ancestorRoute">
            <a class="redirect-parent pointer" @click.prevent="handleAncestorLink(item)">{{
              item.meta.ancestorRouteTitle
            }}</a>
            <span class="separator"> > </span>
            <a class="redirect-parent pointer" @click.prevent="handleAncestorInnerParentLink(item)">{{
              item.meta.parentRouteTitle || getUrlTitle(router.options.history.state.back)
            }}</a>
            <span class="separator"> > </span>
            <span class="no-redirect">{{ item.meta.title || route.query.title }}</span>
          </span>
          <span v-else-if="item.meta.parentRoute">
            <a class="redirect-parent pointer" @click.prevent="handleParentLink(item)">{{
              item.meta.parentRouteTitle || getUrlTitle(router.options.history.state.back)
            }}</a>
            <span class="separator"> > </span>
            <span class="no-redirect">{{ item.meta.title || route.query.title }}</span>
          </span>
          <span v-else class="no-redirect">{{ item.meta.title || route.query.title }}</span>
        </span>
        <a v-else @click.prevent="handleLink(item)">
          {{ item.meta.title }}
        </a>
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<style lang="scss" scoped>
.el-breadcrumb {
  line-height: var(--v3-navigationbar-height);
  .no-redirect {
    color: var(--el-text-color-placeholder);
  }
  a {
    font-weight: normal;
  }
}
</style>
