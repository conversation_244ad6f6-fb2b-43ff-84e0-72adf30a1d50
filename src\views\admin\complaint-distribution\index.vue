<script setup>
import TopTitle from '@/views/components/TopTitle/index.vue'
import { Search, RefreshRight } from '@element-plus/icons-vue'

const searchForm = ref({
  startDate: '', // 开始时间
  endDate: '', // 结束时间
  handlingUnit: '', // 办理单位
  district: '', // 县区
  questionType: '', // 问题分类
  schoolType: '', // 学校类型
  status: '', // 状态
  content: '' // 内容
})

// 办理单位选项
const handlingUnitOptions = [
  { label: '全部', value: '' },
  { label: '省教育厅', value: 'provincial' },
  { label: '市教育局', value: 'municipal' },
  { label: '县教育局', value: 'county' }
]

// 县区选项
const districtOptions = [
  { label: '全部', value: '' },
  { label: '鼓楼区', value: 'gulou' },
  { label: '玄武区', value: 'xuanwu' },
  { label: '秦淮区', value: 'qinhuai' },
  { label: '建邺区', value: 'jianye' },
  { label: '雨花台区', value: 'yuhuatai' }
]

// 问题分类选项
const questionTypeOptions = [
  { label: '全部', value: '' },
  { label: '食品安全', value: 'food_safety' },
  { label: '膳食经费', value: 'meal_funding' },
  { label: '教辅教材征订', value: 'textbook_ordering' },
  { label: '校服定制采购', value: 'uniform_procurement' },
  { label: '校外培训', value: 'external_training' },
  { label: '其他', value: 'other' }
]

// 学校类型选项
const schoolTypeOptions = [
  { label: '全部', value: '' },
  { label: '幼儿园', value: 'kindergarten' },
  { label: '小学', value: 'primary' },
  { label: '初中', value: 'middle' },
  { label: '高中', value: 'high' },
  { label: '大学', value: 'university' }
]

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '待分办', value: 'pending_assignment' },
  { label: '待处理', value: 'pending_processing' },
  { label: '待审核', value: 'pending_review' },
  { label: '审核未通过', value: 'review_failed' },
  { label: '已办结', value: 'completed' },
  { label: '待处理（省厅督办件）', value: 'pending_provincial' },
  { label: '已办结（省厅督办件）', value: 'completed_provincial' }
]

// 日期验证：结束时间不能早于开始时间
const validateDateRange = () => {
  if (searchForm.value.startDate && searchForm.value.endDate) {
    if (new Date(searchForm.value.endDate) < new Date(searchForm.value.startDate)) {
      ElMessage.warning('结束时间不能早于开始时间')
      searchForm.value.endDate = ''
    }
  }
}

// 开始时间变化时的处理
const handleStartDateChange = () => {
  validateDateRange()
}

// 结束时间变化时的处理
const handleEndDateChange = () => {
  validateDateRange()
}

// 搜索功能
const handleSearch = () => {
  // 验证日期范围
  if (searchForm.value.startDate && searchForm.value.endDate) {
    if (new Date(searchForm.value.endDate) < new Date(searchForm.value.startDate)) {
      ElMessage.warning('结束时间不能早于开始时间')
      return
    }
  }

  console.log('搜索参数:', searchForm.value)
  // TODO: 实现搜索逻辑
}

// 重置功能
const handleReset = () => {
  searchForm.value = {
    startDate: '',
    endDate: '',
    handlingUnit: '',
    district: '',
    questionType: '',
    schoolType: '',
    status: '',
    content: ''
  }
  console.log('表单已重置')
}

// 表格数据
const tableData = ref([
  {
    id: '10001',
    city: '南京市',
    district: '玄武区',
    reporterInfo: '第三年级\n156****5581\n学生家长',
    problemType: '膳食经费',
    schoolType: '大学',
    handlingUnit: '南京理工大学',
    problemDescription: '学校收费标准不明确，希望提供详细的收费清单。',
    reportTime: '2025-07-22',
    currentHandler: '南京市教育局',
    deadline: '2025-07-22',
    status: '已办结',
    timeLimit: '2025-07-30',
    operation: '详情办理'
  },
  {
    id: '10002',
    city: '苏州市',
    district: '姑苏区',
    reporterInfo: '第三年级\n156****5581\n学生家长',
    problemType: '校外培训',
    schoolType: '初中',
    handlingUnit: '苏州市第三中学',
    problemDescription: '教师课堂管理不当，希望学校加强师德师风建设和教师培训工作。',
    reportTime: '2025-07-01',
    currentHandler: '南京市教育局',
    deadline: '2025-07-01',
    status: '待处理',
    timeLimit: '2025-07-10',
    operation: '详情办理'
  },
  {
    id: '10003',
    city: '南通市',
    district: '崇川区',
    reporterInfo: '第三年级\n156****5581\n学生家长',
    problemType: '食品安全',
    schoolType: '初中',
    handlingUnit: '上海师范大学',
    problemDescription: '学校周边存在安全隐患问题，希望相关部门加强管理。',
    reportTime: '2025-05-31',
    currentHandler: '南京市教育局',
    deadline: '2025-05-31',
    status: '待分办',
    timeLimit: '2025-06-10',
    operation: '详情办理'
  },
  {
    id: '10004',
    city: '常州市',
    district: '新北区',
    reporterInfo: '第三年级\n156****5581\n学生家长',
    problemType: '教辅教材征订',
    schoolType: '初中',
    handlingUnit: '常州市第三中学',
    problemDescription: '学校教育教学质量有待提高，希望加强教师培训。',
    reportTime: '2025-05-30',
    currentHandler: '南京市教育局',
    deadline: '2025-05-30',
    status: '待审核',
    timeLimit: '2025-08-05',
    operation: '详情办理'
  },
  {
    id: '10005',
    city: '镇江市',
    district: '京口区',
    reporterInfo: '第三年级\n156****5581\n学生家长',
    problemType: '校服定制采购',
    schoolType: '大学',
    handlingUnit: '江苏理工学院',
    problemDescription: '学校收费标准不明确，希望提供详细的收费清单。',
    reportTime: '2025-05-28',
    currentHandler: '南京市教育局',
    deadline: '2025-05-28',
    status: '审核未通过',
    timeLimit: '2025-08-15',
    operation: '详情办理'
  }
])

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 50
})

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page
  console.log('当前页:', page)
}

// 导出数据
const handleExport = () => {
  console.log('导出数据')
  ElMessage.success('数据导出成功')
}

// 操作处理
const handleOperation = (row, action) => {
  console.log('操作:', action, '行数据:', row)
  ElMessage.info(`${action}: ${row.id}`)
}

// 获取状态CSS类名
const getStatusClass = (status) => {
  const classMap = {
    '待分办': 'status-pending-assignment',
    '待处理': 'status-pending-processing',
    '待审核': 'status-pending-review',
    '审核未通过': 'status-review-failed',
    '已办结': 'status-completed',
    '待处理（省厅督办件）': 'status-pending-provincial',
    '已办结（省厅督办件）': 'status-completed-provincial'
  }
  return classMap[status] || 'status-default'
}

// 获取问题分类CSS类名
const getProblemTypeClass = (problemType) => {
  const classMap = {
    '食品安全': 'problem-type-food-safety',
    '膳食经费': 'problem-type-meal-funding',
    '校外培训': 'problem-type-external-training',
    '教辅教材征订': 'problem-type-textbook-ordering',
    '校服定制采购': 'problem-type-uniform-procurement',
    '其他': 'problem-type-other'
  }
  return classMap[problemType] || 'problem-type-other'
}

// 计算剩余天数
const calculateRemainingDays = (timeLimit) => {
  const today = new Date()
  const limitDate = new Date(timeLimit)
  const timeDiff = limitDate.getTime() - today.getTime()
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24))
  return daysDiff
}

// 获取时限显示文本和样式类
const getTimeLimitInfo = (timeLimit, status) => {
  if (status === '已办结') {
    return {
      text: '已完成',
      class: 'time-limit-completed'
    }
  }

  const remainingDays = calculateRemainingDays(timeLimit)

  if (remainingDays < 0) {
    return {
      text: `超期${Math.abs(remainingDays)}天`,
      class: 'time-limit-overdue'
    }
  }

  let className = ''
  if (remainingDays >= 1 && remainingDays < 3) {
    className = 'time-limit-urgent'
  } else if (remainingDays >= 3 && remainingDays <= 5) {
    className = 'time-limit-warning'
  } else if (remainingDays > 5) {
    className = 'time-limit-normal'
  }

  return {
    text: `剩余${remainingDays}天`,
    class: className
  }
}
</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="投诉分办"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" class="search-form-2" label-position="top">
        <!-- 第一行 -->
        <el-form-item label="留言时间段">
          <div class="w-[100%] flex justify-between items-center gap-[8px]">
            <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              placeholder="年/月/日"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              @change="handleStartDateChange"
            />
            <span style="color: #606266; line-height: 48px;">至</span>
            <el-date-picker
              v-model="searchForm.endDate"
              type="date"
              placeholder="年/月/日"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
              :disabled-date="(time) => searchForm.startDate && time < new Date(searchForm.startDate)"
              @change="handleEndDateChange"
            />
          </div>
        </el-form-item>

        <el-form-item label="办理单位">
          <el-select v-model="searchForm.handlingUnit" placeholder="请选择办理单位">
            <el-option
              v-for="item in handlingUnitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="县区">
          <el-select v-model="searchForm.district" placeholder="全部">
            <el-option
              v-for="item in districtOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="问题分类">
          <el-select v-model="searchForm.questionType" placeholder="全部">
            <el-option
              v-for="item in questionTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 第二行 -->
        <el-form-item label="学校类型">
          <el-select v-model="searchForm.schoolType" placeholder="全部">
            <el-option
              v-for="item in schoolTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="内容">
          <el-input
            v-model="searchForm.content"
            placeholder="请输入关键词"
            style="width: 240px"
          />
        </el-form-item>

        <!-- 按钮组 -->
        <el-form-item class="hidden-label" label="按钮">
          <el-button class="common-button-3" @click="handleSearch">
            <template #icon>
              <el-icon>
                <Search />
              </el-icon>
            </template>
            查询
          </el-button>
          <el-button class="reset-button" @click="handleReset">
            <template #icon>
              <el-icon><RefreshRight /></el-icon>
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </section>
    <section class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <!-- 导出按钮 -->
      <div class="mb-[16px] flex justify-between">
        <el-button class="common-button-4">
          <template #icon>
            <el-icon><RefreshRight /></el-icon>
          </template>
          刷新
        </el-button>
        <el-button class="common-button-3" @click="handleExport">
          <template #icon>
            <img src="@/assets/images/common/export.png" alt="" width="16" height="16">
          </template>
          导出数据
        </el-button>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 固定左侧列 -->
        <el-table-column prop="id" label="ID" width="80" fixed="left" align="center" />
        <el-table-column prop="city" label="市区" width="100" fixed="left" align="center" />
        <el-table-column prop="district" label="县区" width="100" fixed="left" align="center" />
        <el-table-column label="举报人信息" width="120" fixed="left" align="center">
          <template #default="{ row }">
            <div class="whitespace-pre-line">{{ row.reporterInfo }}</div>
          </template>
        </el-table-column>

        <!-- 其他列 -->
        <el-table-column label="问题分类" width="180" align="center">
          <template #default="{ row }">
            <span
              :class="['problem-type-tag', getProblemTypeClass(row.problemType)]"
            >
              {{ row.problemType }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="schoolType" label="学校类型" width="100" align="center" />
        <el-table-column prop="handlingUnit" label="举报学校名称" width="150" align="center" />
        <el-table-column label="举报内容" min-width="200" align="center">
          <template #default="{ row }">
            <div>{{ row.problemDescription }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="reportTime" label="留言时间" width="120" align="center" />
        <el-table-column prop="currentHandler" label="办理单位" width="120" align="center" />
        <el-table-column prop="deadline" label="公办时间" width="120" align="center" />
        <el-table-column label="状态" width="200" align="center">
          <template #default="{ row }">
            <span
              :class="['status-tag', getStatusClass(row.status)]"
            >
              {{ row.status }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="时限" width="120" align="center">
          <template #default="{ row }">
            <span
              :class="['time-limit-tag', getTimeLimitInfo(row.timeLimit, row.status).class]"
            >
              {{ getTimeLimitInfo(row.timeLimit, row.status).text }}
            </span>
          </template>
        </el-table-column>

        <!-- 固定右侧操作列 -->
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <div
              class="pointer blue2"
              @click="handleOperation(row, '问题分办')"
            >
              问题分办
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>
 </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 时限标签基础样式
.time-limit-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

// 时限样式类
.time-limit-completed {
  color: #14AD67;
}

.time-limit-urgent {
  color: #E72C4A;
  border-left: 3px solid #E72C4A;
  padding-left: 8px;
}

.time-limit-warning {
  color: #FF9200;
  border-left: 3px solid #FF9200;
  padding-left: 8px;
}

.time-limit-normal {
  color: #14AD67;
  border-left: 3px solid #14AD67;
  padding-left: 8px;
}

.time-limit-overdue {
  color: #E72C4A;
  border-left: 3px solid #E72C4A;
  padding-left: 8px;
}


</style>
