import type * as Auth from "./type"
import { request } from "@/utils/axios"

/** 获取登录验证码 */
export function getCaptchaApi() {
  return request<Auth.CaptchaResponseData>({
    url: "auth/captcha",
    method: "get"
  })
}

/** 发送手机验证码 */
export function sendSmsCodeApi(data: Auth.SendSmsCodeRequestData) {
  return request<Auth.SendSmsCodeResponseData>({
    url: "auth/sms-code",
    method: "post",
    data
  })
}

/** 登录并返回 Token */
export function loginApi(data: Auth.LoginRequestData) {
  return request<Auth.LoginResponseData>({
    url: "auth/login",
    method: "post",
    data
  })
}
